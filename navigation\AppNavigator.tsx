import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { LoginScreen } from '../screens/LoginScreen';
import { ThirdSectionScreen } from '../screens/ThirdSectionScreen';
import { WhoAreYouScreen } from '../screens/WhoAreYouScreen';
import { WhereAreYouScreen } from '../screens/WhereAreYouScreen';
import { SocialScreen } from '../screens/SocialScreen';
import { WhatAreYouScreen } from '../screens/WhatAreYouScreen';
import { HowAreYouScreen } from '../screens/HowAreYouScreen';
import { QuestionnaireIntroScreen } from '../screens/QuestionnaireIntroScreen';
import { QuestionnaireScreen } from '../screens/QuestionnaireScreen';
import { SoundQuestionnaireScreen } from '../screens/SoundQuestionnaireScreen';
import { EnergyQuestionnaireScreen } from '../screens/EnergyQuestionnaireScreen';
import { IntentionsQuestionnaireScreen } from '../screens/IntentionsQuestionnaireScreen';
import { TabNavigator } from './TabNavigator';
import { useAuth } from '../context/AuthContext';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

const Stack = createStackNavigator();

export const AppNavigator: React.FC = () => {
  const { session, loading } = useAuth();

  console.log('AppNavigator: session =', session ? 'Active' : 'None', 'loading =', loading);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      {session ? (
        <>
          <Stack.Screen name="Main" component={TabNavigator} />
          <Stack.Screen name="ThirdSection" component={ThirdSectionScreen} />
          <Stack.Screen name="WhoAreYou" component={WhoAreYouScreen} />
          <Stack.Screen name="WhereAreYou" component={WhereAreYouScreen} />
          <Stack.Screen name="Social" component={SocialScreen} />
          <Stack.Screen name="WhatAreYou" component={WhatAreYouScreen} />
          <Stack.Screen name="HowAreYou" component={HowAreYouScreen} />
          <Stack.Screen name="QuestionnaireIntro" component={QuestionnaireIntroScreen} />
          <Stack.Screen name="Questionnaire" component={QuestionnaireScreen} />
          <Stack.Screen name="SoundQuestionnaire" component={SoundQuestionnaireScreen} />
          <Stack.Screen name="EnergyQuestionnaire" component={EnergyQuestionnaireScreen} />
          <Stack.Screen name="IntentionsQuestionnaire" component={IntentionsQuestionnaireScreen} />
        </>
      ) : (
        <>
          <Stack.Screen name="Login" component={LoginScreen} />
          <Stack.Screen name="ThirdSection" component={ThirdSectionScreen} />
          <Stack.Screen name="WhoAreYou" component={WhoAreYouScreen} />
          <Stack.Screen name="WhereAreYou" component={WhereAreYouScreen} />
          <Stack.Screen name="Social" component={SocialScreen} />
          <Stack.Screen name="WhatAreYou" component={WhatAreYouScreen} />
          <Stack.Screen name="HowAreYou" component={HowAreYouScreen} />
          <Stack.Screen name="QuestionnaireIntro" component={QuestionnaireIntroScreen} />
          <Stack.Screen name="Questionnaire" component={QuestionnaireScreen} />
          <Stack.Screen name="SoundQuestionnaire" component={SoundQuestionnaireScreen} />
          <Stack.Screen name="EnergyQuestionnaire" component={EnergyQuestionnaireScreen} />
          <Stack.Screen name="IntentionsQuestionnaire" component={IntentionsQuestionnaireScreen} />
        </>
      )}
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
  },
});
