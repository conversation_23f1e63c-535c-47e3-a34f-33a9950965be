import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Dummy data for the swipe feed
const dummyProjects = [
  {
    id: '1',
    title: 'Looking for a guitarist',
    artist: '<PERSON>',
    genre: 'Indie Rock',
    description: 'Working on some chill indie tracks, need someone with good rhythm skills',
    avatar: 'https://via.placeholder.com/60',
  },
  {
    id: '2',
    title: 'Hip-hop beats collab',
    artist: '<PERSON>',
    genre: 'Hip-Hop',
    description: 'Got some fire beats, looking for a rapper to lay down some vocals',
    avatar: 'https://via.placeholder.com/60',
  },
  {
    id: '3',
    title: 'Jazz fusion experiment',
    artist: '<PERSON>',
    genre: 'Jazz',
    description: 'Exploring some fusion concepts, need bass and drums',
    avatar: 'https://via.placeholder.com/60',
  },
];

export const SwipeFeedScreen: React.FC = () => {
  const renderProjectCard = ({ item }: { item: typeof dummyProjects[0] }) => (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <Image source={{ uri: item.avatar }} style={styles.avatar} />
        <View style={styles.headerInfo}>
          <Text style={styles.artistName}>{item.artist}</Text>
          <Text style={styles.genre}>{item.genre}</Text>
        </View>
        <TouchableOpacity style={styles.moreButton}>
          <Ionicons name="ellipsis-horizontal" size={20} color="#888" />
        </TouchableOpacity>
      </View>
      
      <Text style={styles.projectTitle}>{item.title}</Text>
      <Text style={styles.description}>{item.description}</Text>
      
      <View style={styles.cardActions}>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="heart-outline" size={24} color="#ff6b6b" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="chatbubble-outline" size={24} color="#007AFF" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="share-outline" size={24} color="#888" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Discover</Text>
        <TouchableOpacity style={styles.filterButton}>
          <Ionicons name="funnel-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>
      
      <FlatList
        data={dummyProjects}
        renderItem={renderProjectCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.feedContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
  },
  filterButton: {
    padding: 8,
  },
  feedContainer: {
    padding: 16,
  },
  card: {
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  headerInfo: {
    flex: 1,
  },
  artistName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  genre: {
    fontSize: 14,
    color: '#888',
  },
  moreButton: {
    padding: 4,
  },
  projectTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: '#ccc',
    lineHeight: 20,
    marginBottom: 16,
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#333',
  },
  actionButton: {
    padding: 8,
  },
});
