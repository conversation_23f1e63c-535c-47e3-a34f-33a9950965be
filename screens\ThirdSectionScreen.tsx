import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

type RootStackParamList = {
  WhoAreYou: undefined;
  WhereAreYou: undefined;
  Social: undefined;
  WhatAreYou: undefined;
  HowAreYou: undefined;
};

export const ThirdSectionScreen: React.FC = () => {
  const navigation = useNavigation<any>();

  const handleNext = () => {
    navigation.navigate('WhoAreYou' as never);
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Back Button */}
      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Ionicons name="arrow-back" size={24} color="#2563EB" />
      </TouchableOpacity>

      <View style={styles.content}>
        <View style={styles.textContainer}>
          <Text style={styles.thirdSectionText}>you</Text>
          <Text style={styles.thirdSectionMixText}>MIX</Text>
          <Text style={styles.thirdSectionText}>well!</Text>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
            <Text style={styles.nextButtonText}>Next</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB", // bg-gray-50 equivalent
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
    padding: 10,
  },
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20, // px-5
    paddingVertical: 80, // py-20
  },
  textContainer: {
    alignItems: "center",
    marginBottom: 16, // space-y-4 equivalent
  },
  thirdSectionText: {
    fontSize: 24, // text-2xl
    color: "#2563EB", // text-blue-600
    fontWeight: "500", // font-medium
    marginBottom: 16, // space-y-4 equivalent
    textAlign: "center",
  },
  thirdSectionMixText: {
    fontSize: 72, // text-7xl equivalent
    fontWeight: "bold", // font-bold
    color: "#2563EB", // text-blue-600
    textAlign: "center",
    letterSpacing: 2, // tracking-wider
    marginBottom: 16,
  },
  buttonContainer: {
    marginBottom: 40, // mb-10
  },
  nextButton: {
    backgroundColor: "#1E40AF", // bg-blue-800
    paddingHorizontal: 32, // px-8
    paddingVertical: 12, // py-3
    borderRadius: 6, // rounded-md
  },
  nextButtonText: {
    color: "#FFFFFF", // text-white
    fontSize: 16,
    fontWeight: "500", // font-medium
    textAlign: "center",
  },
});
