import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView, Dimensions } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

export const IntentionsQuestionnaireScreen: React.FC = () => {
  const navigation = useNavigation();
  const [selectedPrompts, setSelectedPrompts] = useState<number[]>([]);

  const prompts = [
    "I am looking for...",
    "I'm looking to connect with...",
    "Where I'm at creatively...",
    "I have a collaboration with...",
    "I prefer my workspace to be...",
    "My ideal studio sesh has...",
  ];

  const togglePrompt = (index: number) => {
    setSelectedPrompts((prev) => 
      prev.includes(index) 
        ? prev.filter((i) => i !== index) 
        : [...prev, index]
    );
  };

  const handleNext = () => {
    if (selectedPrompts.length === 0) return;
    
    console.log('Selected intentions prompts:', selectedPrompts);
    console.log('Questionnaire flow completed - navigating to main app');
    // Navigate to the main app (chat screen)
    navigation.navigate('Main' as never);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.background}>
        {/* Orange gradient effects */}
        <View style={[styles.gradientBlob, styles.topLeftBlob]} />
        <View style={[styles.gradientBlob, styles.bottomRightBlob]} />
        <View style={[styles.gradientBlob, styles.topRightBlob]} />

        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.content}>
            {/* Main title */}
            <View style={styles.titleContainer}>
              <Text style={styles.mainTitle}>your intentions</Text>
            </View>

            {/* Scroll indicator */}
            <View style={styles.scrollIndicator}>
              <Text style={styles.scrollText}>scroll</Text>
              <View style={styles.arrowContainer}>
                <Ionicons name="arrow-down" size={16} color="#FFFFFF" />
              </View>
            </View>

            {/* Instructions */}
            <View style={styles.instructionsContainer}>
              <Text style={styles.instructionsText}>
                pick at least 1 prompt{'\n'}to fill out
              </Text>
            </View>

            {/* Prompt cards */}
            <View style={styles.promptsContainer}>
              {prompts.map((prompt, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => togglePrompt(index)}
                  style={[
                    styles.promptButton,
                    selectedPrompts.includes(index) ? styles.promptButtonSelected : styles.promptButtonUnselected
                  ]}
                >
                  <Text style={[
                    styles.promptText,
                    selectedPrompts.includes(index) ? styles.promptTextSelected : styles.promptTextUnselected
                  ]}>
                    {prompt}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Next button */}
            <TouchableOpacity
              style={[
                styles.nextButton,
                selectedPrompts.length === 0 && styles.nextButtonDisabled
              ]}
              onPress={handleNext}
              disabled={selectedPrompts.length === 0}
            >
              <Text style={[
                styles.nextButtonText,
                selectedPrompts.length === 0 && styles.nextButtonTextDisabled
              ]}>
                next
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    backgroundColor: '#000000', // bg-black
    position: 'relative',
  },
  gradientBlob: {
    position: 'absolute',
    borderRadius: 999,
  },
  topLeftBlob: {
    width: 160, // w-40
    height: 160, // h-40
    backgroundColor: '#F97316', // bg-orange-500
    top: 80, // top-20
    left: 40, // left-10
    opacity: 0.6,
  },
  bottomRightBlob: {
    width: 128, // w-32
    height: 128, // h-32
    backgroundColor: '#F59E0B', // bg-amber-400
    bottom: 160, // bottom-40
    right: 40, // right-10
    opacity: 0.5,
  },
  topRightBlob: {
    width: 96, // w-24
    height: 96, // h-24
    backgroundColor: '#EAB308', // bg-yellow-500
    top: '33%', // top-1/3
    right: 80, // right-20
    opacity: 0.4,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 24, // px-6
    paddingVertical: 32, // py-8
    position: 'relative',
    zIndex: 10,
  },
  content: {
    alignItems: 'center',
    minHeight: '100%',
    justifyContent: 'center',
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 64, // mb-16
  },
  mainTitle: {
    fontSize: 36, // text-4xl
    fontWeight: 'bold',
    color: '#FB923C', // text-orange-400
    marginBottom: 32, // mb-8
    textAlign: 'center',
    textShadowColor: 'rgba(251, 146, 60, 0.8)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 20,
  },
  scrollIndicator: {
    alignItems: 'center',
    marginBottom: 48, // mb-12
  },
  scrollText: {
    color: '#FFFFFF',
    fontSize: 14, // text-sm
    marginBottom: 8, // mb-2
  },
  arrowContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructionsContainer: {
    alignItems: 'center',
    marginBottom: 32, // mb-8
  },
  instructionsText: {
    color: '#FFFFFF',
    fontSize: 14, // text-sm
    textAlign: 'center',
    lineHeight: 20,
  },
  promptsContainer: {
    width: '100%',
    maxWidth: 320, // max-w-sm
    marginBottom: 32, // mb-8
  },
  promptButton: {
    width: '100%',
    padding: 16, // p-4
    borderRadius: 25, // rounded-full
    marginBottom: 12, // space-y-3
    borderWidth: 1,
  },
  promptButtonSelected: {
    backgroundColor: 'rgba(249, 115, 22, 0.3)', // bg-orange-500/30
    borderColor: '#FB923C', // border-orange-400
  },
  promptButtonUnselected: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // bg-black/50
    borderColor: '#4B5563', // border-gray-600
  },
  promptText: {
    fontSize: 14, // text-sm
    textAlign: 'left',
  },
  promptTextSelected: {
    color: '#FED7AA', // text-orange-200
  },
  promptTextUnselected: {
    color: '#D1D5DB', // text-gray-300
  },
  nextButton: {
    paddingHorizontal: 32, // px-8
    paddingVertical: 12, // py-3
    borderRadius: 8, // rounded-lg
    backgroundColor: '#2563EB', // bg-blue-600
  },
  nextButtonDisabled: {
    backgroundColor: '#4B5563', // bg-gray-600
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontWeight: '500', // font-medium
    textAlign: 'center',
    fontSize: 16,
  },
  nextButtonTextDisabled: {
    color: '#9CA3AF', // text-gray-400
  },
});
