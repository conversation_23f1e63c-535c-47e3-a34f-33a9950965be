import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, Dimensions, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

export const QuestionnaireScreen: React.FC = () => {
  const navigation = useNavigation();

  const handleScrollPress = () => {
    navigation.navigate('SoundQuestionnaire' as never);
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#7F1D1D', '#DC2626', '#DC2626']} // from-red-900 via-red-700 to-red-600
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* MIX Branding */}
          <View style={styles.brandingContainer}>
            <Text style={styles.theText}>the</Text>
            <Text style={styles.mixText}>MIX</Text>
          </View>

          {/* Instructions Text */}
          <View style={styles.instructionsContainer}>
            <Text style={styles.instructionsText}>
              answer or fill out some of these prompts that you would like other artist to know about you
            </Text>
          </View>

          {/* Scroll Indicator */}
          <TouchableOpacity style={styles.scrollIndicatorContainer} onPress={handleScrollPress}>
            <Text style={styles.scrollText}>scroll</Text>
            <View style={styles.chevronContainer}>
              <Ionicons name="chevron-forward" size={24} color="#FFFFFF" />
            </View>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 32, // px-8
    paddingVertical: 48, // py-12
  },
  brandingContainer: {
    alignItems: 'center',
    marginTop: 32, // mt-8
  },
  theText: {
    fontSize: 18, // text-lg
    fontWeight: '300', // font-light
    color: '#3B82F6', // text-blue-500
    marginBottom: 8, // mb-2
    textShadowColor: 'rgba(59, 130, 246, 0.8)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 15,
  },
  mixText: {
    fontSize: 96, // text-8xl equivalent
    fontWeight: 'bold', // font-bold
    color: '#3B82F6', // text-blue-500
    letterSpacing: 4, // tracking-wider
    textShadowColor: 'rgba(59, 130, 246, 0.9)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 25,
  },
  instructionsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16, // px-4
    maxWidth: width * 0.8, // max-w-sm equivalent
  },
  instructionsText: {
    color: '#FFFFFF', // text-white
    fontSize: 20, // text-xl
    lineHeight: 32, // leading-relaxed
    textAlign: 'center',
  },
  scrollIndicatorContainer: {
    alignItems: 'center',
    marginBottom: 32, // mb-8
  },
  scrollText: {
    color: '#FFFFFF', // text-white
    fontSize: 18, // text-lg
    marginBottom: 8, // mb-2
  },
  chevronContainer: {
    // Simple chevron without complex animation for now
    // You can add animations later if needed
  },
});
