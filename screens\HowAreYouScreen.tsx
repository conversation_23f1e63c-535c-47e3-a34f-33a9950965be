import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

export const HowAreYouScreen: React.FC = () => {
  const navigation = useNavigation();
  const [selectedGenres, setSelectedGenres] = useState<string[]>([]);

  const genres = [
    'Pop',
    'Rock',
    'Soul',
    'Jazz',
    'Rap',
    'Alt'
  ];

  const toggleGenre = (genre: string) => {
    setSelectedGenres(prev => 
      prev.includes(genre) 
        ? prev.filter(g => g !== genre)
        : [...prev, genre]
    );
  };

  const handleSubmit = () => {
    // Validate that at least one genre is selected
    if (selectedGenres.length === 0) {
      // You can add an alert here if you want
      return;
    }
    
    // Here you would typically save all the onboarding data
    console.log('Onboarding completed! Selected genres:', selectedGenres);
    
    // Navigate to the Questionnaire Intro screen
    navigation.navigate('QuestionnaireIntro' as never);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Back Button */}
      <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
        <Ionicons name="arrow-back" size={24} color="#2563EB" />
      </TouchableOpacity>

      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.content}>
          <Text style={styles.title}>How are you?</Text>
          
          <View style={styles.optionsContainer}>
            {genres.map((genre) => (
              <TouchableOpacity
                key={genre}
                style={styles.optionRow}
                onPress={() => toggleGenre(genre)}
              >
                <View style={styles.checkboxContainer}>
                  {selectedGenres.includes(genre) ? (
                    <View style={styles.checkboxChecked}>
                      <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                    </View>
                  ) : (
                    <View style={styles.checkboxUnchecked} />
                  )}
                </View>
                <Text style={styles.optionText}>{genre}</Text>
              </TouchableOpacity>
            ))}
          </View>

          <TouchableOpacity 
            style={[
              styles.submitButton, 
              selectedGenres.length === 0 && styles.submitButtonDisabled
            ]} 
            onPress={handleSubmit}
            disabled={selectedGenres.length === 0}
          >
            <Text style={styles.submitButtonText}>Submit</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollContainer: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 40,
    textAlign: 'center',
  },
  optionsContainer: {
    width: '100%',
    maxWidth: 400,
    marginBottom: 40,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  checkboxContainer: {
    marginRight: 16,
  },
  checkboxUnchecked: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  checkboxChecked: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#1E40AF',
    borderRadius: 4,
    backgroundColor: '#1E40AF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 18,
    color: '#1F2937',
    fontWeight: '500',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
    padding: 10,
  },
  submitButton: {
    backgroundColor: '#1E40AF',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
  },
  submitButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});
