import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Dummy data for circles
const dummyCircles = [
  {
    id: '1',
    name: 'Indie Rockers',
    members: 24,
    description: 'College indie musicians sharing tracks and collaborating',
    avatar: 'https://via.placeholder.com/80',
    isJoined: true,
  },
  {
    id: '2',
    name: 'Hip-Hop Collective',
    members: 31,
    description: 'Beats, rhymes, and everything hip-hop',
    avatar: 'https://via.placeholder.com/80',
    isJoined: false,
  },
  {
    id: '3',
    name: 'Jazz Explorers',
    members: 18,
    description: 'Experimental jazz and fusion projects',
    avatar: 'https://via.placeholder.com/80',
    isJoined: true,
  },
  {
    id: '4',
    name: 'Electronic Vibes',
    members: 42,
    description: 'EDM, house, techno, and everything electronic',
    avatar: 'https://via.placeholder.com/80',
    isJoined: false,
  },
];

export const CirclesScreen: React.FC = () => {
  const renderCircleCard = ({ item }: { item: typeof dummyCircles[0] }) => (
    <View style={styles.card}>
      <Image source={{ uri: item.avatar }} style={styles.circleAvatar} />
      <View style={styles.circleInfo}>
        <Text style={styles.circleName}>{item.name}</Text>
        <Text style={styles.memberCount}>{item.members} members</Text>
        <Text style={styles.circleDescription}>{item.description}</Text>
      </View>
      <TouchableOpacity 
        style={[
          styles.joinButton,
          item.isJoined && styles.joinedButton
        ]}
      >
        <Text style={[
          styles.joinButtonText,
          item.isJoined && styles.joinedButtonText
        ]}>
          {item.isJoined ? 'Joined' : 'Join'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Circles</Text>
        <TouchableOpacity style={styles.createButton}>
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>
      
      <View style={styles.tabContainer}>
        <TouchableOpacity style={[styles.tab, styles.activeTab]}>
          <Text style={[styles.tabText, styles.activeTabText]}>Discover</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.tab}>
          <Text style={styles.tabText}>My Circles</Text>
        </TouchableOpacity>
      </View>
      
      <FlatList
        data={dummyCircles}
        renderItem={renderCircleCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
  },
  createButton: {
    padding: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    color: '#888',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  listContainer: {
    padding: 16,
  },
  card: {
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  circleAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  circleInfo: {
    flex: 1,
  },
  circleName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 4,
  },
  memberCount: {
    fontSize: 14,
    color: '#888',
    marginBottom: 4,
  },
  circleDescription: {
    fontSize: 14,
    color: '#ccc',
    lineHeight: 18,
  },
  joinButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },
  joinedButton: {
    backgroundColor: '#2a2a2a',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  joinButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  joinedButtonText: {
    color: '#007AFF',
  },
});
