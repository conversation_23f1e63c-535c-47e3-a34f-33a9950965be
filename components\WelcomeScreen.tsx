import React, { useRef } from "react"
import { View, Text, StyleSheet, SafeAreaView, ScrollView, TouchableOpacity } from "react-native"
import { Ionicons } from "@expo/vector-icons"
import { useNavigation } from '@react-navigation/native';

export default function WelcomeScreen() {
  const scrollViewRef = useRef<ScrollView>(null);
  const navigation = useNavigation();

  const handleVinylPress = () => {
    // Navigate to the third section screen
    navigation.navigate('ThirdSection' as never);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        ref={scrollViewRef}
        contentContainerStyle={styles.scrollContainer}
      >
        {/* First Section - Welcome */}
        <View style={styles.welcomeSection}>
          <View style={styles.welcomeContent}>
            <View style={styles.textContainer}>
              <Text style={styles.welcomeText}>Welcome</Text>
              <Text style={styles.toText}>to</Text>
              <Text style={styles.theText}>the</Text>
              <Text style={styles.mixText}>MIX</Text>
            </View>
          </View>

          <View style={styles.scrollIndicator}>
            <Text style={styles.scrollText}>scroll</Text>
            <Ionicons name="chevron-down" size={20} color="#2D3748" style={styles.chevron} />
          </View>
        </View>

        {/* Second Section - Demystify */}
        <View style={styles.demystifySection}>
          <View style={styles.demystifyTextContainer}>
            <Text style={styles.demystifyText}>Demystify the music industry.</Text>
          </View>

          <View style={styles.imageContainer}>
            {/* Vinyl Record Base - Now Touchable */}
            <TouchableOpacity 
              style={styles.vinylRecordContainer}
              onPress={handleVinylPress}
              activeOpacity={0.8}
            >
              <View style={styles.vinylRecord}>
                {/* Center Label */}
                <View style={styles.centerLabel}>
                  <Text style={styles.centerLabelText}>the</Text>
                  <Text style={styles.centerLabelMixText}>MIX</Text>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB", // bg-gray-50 equivalent
  },
  scrollContainer: {
    flexGrow: 1,
  },
  // First Section - Welcome
  welcomeSection: {
    minHeight: 800, // min-h-screen equivalent
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20, // px-5 equivalent
  },
  welcomeContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  textContainer: {
    alignItems: "center",
    marginBottom: 8, // space-y-2 equivalent
  },
  welcomeText: {
    fontSize: 48, // text-5xl equivalent
    fontWeight: "600", // font-semibold
    color: "#1F2937", // text-gray-800
    marginBottom: 8, // mb-2
    textAlign: "center",
  },
  toText: {
    fontSize: 24, // text-2xl
    color: "#4B5563", // text-gray-600
    marginBottom: 16, // mb-4
    textAlign: "center",
  },
  theText: {
    fontSize: 30, // text-3xl
    color: "#2563EB", // text-blue-600
    marginBottom: 8, // mb-2
    textAlign: "center",
  },
  mixText: {
    fontSize: 72, // text-7xl equivalent
    fontWeight: "bold", // font-bold
    color: "#2563EB", // text-blue-600
    textAlign: "center",
    letterSpacing: 2, // tracking-wider
  },
  scrollIndicator: {
    alignItems: "center",
    marginBottom: 40, // mb-10 equivalent
  },
  scrollText: {
    fontSize: 18, // text-lg
    color: "#1F2937", // text-gray-800
    marginBottom: 8, // mb-2
  },
  chevron: {
    marginTop: 4,
  },
  // Second Section - Demystify
  demystifySection: {
    minHeight: 800, // min-h-screen equivalent
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20, // px-5
    paddingVertical: 80, // py-20
  },
  demystifyTextContainer: {
    alignItems: "center",
    marginBottom: 64, // mb-16
  },
  demystifyText: {
    fontSize: 30, // text-3xl equivalent
    fontWeight: "600", // font-semibold
    color: "#1F2937", // text-gray-800
    textAlign: "center",
    lineHeight: 36,
  },
  imageContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  vinylRecordContainer: {
    width: 320, // w-80 equivalent
    height: 320, // h-80 equivalent
  },
  vinylRecord: {
    width: "100%",
    height: "100%",
    borderRadius: 160, // rounded-full
    backgroundColor: "#1a1a1a", // Dark vinyl color
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
    elevation: 16, // shadow-2xl equivalent
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  centerLabel: {
    width: 96, // w-24 equivalent
    height: 96, // h-24 equivalent
    backgroundColor: "#FFFFFF",
    borderRadius: 48, // rounded-full
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8, // shadow-lg equivalent
  },
  centerLabelText: {
    fontSize: 12, // text-xs equivalent
    color: "#2563EB", // text-blue-600
    fontWeight: "500", // font-medium
    marginBottom: 2,
  },
  centerLabelMixText: {
    fontSize: 18, // text-lg equivalent
    fontWeight: "bold", // font-bold
    color: "#2563EB", // text-blue-600
    letterSpacing: 1, // tracking-wide
  },
})
