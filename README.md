# Sesh - Music Collaboration App

A React Native mobile app built with Expo for college creatives to collaborate on music projects.

## Features

- 🎵 **Swipe Feed**: Discover music collaboration opportunities
- 👥 **Circles**: Join music communities and groups
- 💬 **Messaging**: Chat with collaborators
- 👤 **Profile**: Showcase your musical identity
- 🔐 **Authentication**: Secure login with Supabase

## Tech Stack

- **React Native** with Expo
- **React Navigation** for screen transitions
- **Supabase** for authentication, database, and storage
- **Expo AV** for audio playback
- **TypeScript** for type safety

## Project Structure

```
Zesh_MVP/
├── screens/           # Main app screens
│   ├── LoginScreen.tsx
│   ├── SwipeFeedScreen.tsx
│   ├── CirclesScreen.tsx
│   ├── ChatScreen.tsx
│   └── ProfileScreen.tsx
├── navigation/        # Navigation configuration
│   ├── AppNavigator.tsx
│   └── TabNavigator.tsx
├── context/          # React context providers
│   └── AuthContext.tsx
├── services/         # Backend services
│   └── supabase.ts
├── components/       # Reusable UI components
│   ├── LoadingSpinner.tsx
│   └── index.ts
└── assets/          # Images and static assets
```

## Setup Instructions

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Configure Supabase**:
   - Create a new project at [supabase.com](https://supabase.com)
   - Copy your project URL and anon key
   - Update `services/supabase.ts` with your credentials:
     ```typescript
     const supabaseUrl = 'YOUR_SUPABASE_URL';
     const supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
     ```

3. **Run the app**:
   ```bash
   # Start the development server
   npm start
   
   # Or run on specific platform
   npm run ios     # iOS simulator
   npm run android # Android emulator
   npm run web     # Web browser
   ```

## Key Components

### Authentication
- Managed through `AuthContext.tsx`
- Supports sign up, sign in, and sign out
- Automatically handles session persistence

### Navigation
- Bottom tab navigation with 4 main screens
- Stack navigation for authentication flow
- Custom styling with dark theme

### Screens
- **Login**: Authentication with email/password
- **Home (Swipe Feed)**: Discover collaboration projects
- **Circles**: Join and browse music communities
- **Messages**: Chat interface with recent conversations
- **Profile**: User profile with stats and settings

## Next Steps

To continue development:

1. Set up your Supabase database schema
2. Implement actual data fetching in screens
3. Add audio playback functionality with Expo AV
4. Integrate push notifications
5. Add image/audio upload capabilities
6. Implement real-time messaging

## Development Notes

- All screens currently use dummy data for UI development
- Authentication flow is functional but requires Supabase setup
- Dark theme is implemented throughout the app
- TypeScript is configured for better development experience
