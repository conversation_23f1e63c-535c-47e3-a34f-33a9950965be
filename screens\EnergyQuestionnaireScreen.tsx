import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView, Dimensions } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

export const EnergyQuestionnaireScreen: React.FC = () => {
  const navigation = useNavigation();
  const [selectedColor, setSelectedColor] = useState<string>("");
  const [selectedPrompts, setSelectedPrompts] = useState<Set<number>>(new Set());

  const colors = [
    { id: "purple", color: "#8B5CF6" },
    { id: "blue", color: "#3B82F6" },
    { id: "red", color: "#EF4444" },
    { id: "orange", color: "#F97316" },
    { id: "green", color: "#10B981" },
  ];

  const prompts = [
    "what's your last single cover... or your next one :)",
    "my creative flow feels...",
    "in a collaboration I'm usually the... does all that stuff",
    "in a session I could improve on...",
    "my energy is... (contest at the studio)",
    "when I'm creating, I usually...",
  ];

  const togglePrompt = (index: number) => {
    const newSelected = new Set(selectedPrompts);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedPrompts(newSelected);
  };

  const canProceed = selectedPrompts.size > 0;

  const handleNext = () => {
    if (!canProceed) return;
    
    console.log('Selected color:', selectedColor);
    console.log('Selected prompts:', Array.from(selectedPrompts));
    // Navigate to intentions questionnaire screen
    navigation.navigate('IntentionsQuestionnaire' as never);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.background}>
        {/* Yellow gradient effects */}
        <View style={[styles.gradientBlob, styles.topLeftBlob]} />
        <View style={[styles.gradientBlob, styles.bottomRightBlob]} />

        <ScrollView contentContainerStyle={styles.scrollContainer}>
          {/* Header section */}
          <View style={styles.headerSection}>
            <Text style={styles.headerTitle}>what's your energy?</Text>

            <View style={styles.scrollIndicator}>
              <Text style={styles.scrollText}>scroll</Text>
              <Ionicons name="chevron-down" size={16} color="#FFFFFF" />
            </View>
          </View>

          {/* Color selection */}
          <View style={styles.colorSection}>
            <Text style={styles.sectionTitle}>select your profile color to match your aesthetic</Text>
            <View style={styles.colorRow}>
              {colors.map((colorOption) => (
                <TouchableOpacity
                  key={colorOption.id}
                  onPress={() => setSelectedColor(colorOption.id)}
                  style={[
                    styles.colorButton,
                    { backgroundColor: colorOption.color },
                    selectedColor === colorOption.id && styles.colorButtonSelected
                  ]}
                />
              ))}
            </View>
          </View>

          {/* Prompts section */}
          <View style={styles.promptsSection}>
            <Text style={styles.sectionTitle}>pick at least 1 prompt to fill out</Text>

            <View style={styles.promptsList}>
              {prompts.map((prompt, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => togglePrompt(index)}
                  style={[
                    styles.promptButton,
                    selectedPrompts.has(index) ? styles.promptButtonSelected : styles.promptButtonUnselected
                  ]}
                >
                  <Text style={[
                    styles.promptText,
                    selectedPrompts.has(index) ? styles.promptTextSelected : styles.promptTextUnselected
                  ]}>
                    {prompt}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Next button */}
          <TouchableOpacity
            style={[
              styles.nextButton,
              !canProceed && styles.nextButtonDisabled
            ]}
            onPress={handleNext}
            disabled={!canProceed}
          >
            <Text style={[
              styles.nextButtonText,
              !canProceed && styles.nextButtonTextDisabled
            ]}>
              next
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    backgroundColor: '#000000', // bg-black
    position: 'relative',
  },
  gradientBlob: {
    position: 'absolute',
    backgroundColor: '#FACC15', // yellow-400
    borderRadius: 999,
    opacity: 0.3,
  },
  topLeftBlob: {
    width: 256, // w-64
    height: 256, // h-64
    top: -128, // -translate-y-1/2
    left: -128, // -translate-x-1/2
    opacity: 0.3,
  },
  bottomRightBlob: {
    width: 192, // w-48
    height: 192, // h-48
    bottom: '33%', // bottom-1/3
    right: -96, // translate-x-1/2
    opacity: 0.25,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 24, // px-6
    paddingVertical: 48, // py-12
    position: 'relative',
    zIndex: 10,
  },
  headerSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    marginBottom: 32, // mb-8
    minHeight: 300,
  },
  headerTitle: {
    fontSize: 36, // text-4xl
    fontWeight: 'bold',
    color: '#FACC15', // text-yellow-400
    marginBottom: 64, // mb-16
    textAlign: 'center',
    textShadowColor: 'rgba(250, 204, 21, 0.5)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 20,
  },
  scrollIndicator: {
    alignItems: 'center',
    marginBottom: 32, // mb-8
  },
  scrollText: {
    color: '#FFFFFF',
    fontSize: 14, // text-sm
    marginBottom: 8, // mb-2
  },
  colorSection: {
    marginBottom: 32, // mb-8
  },
  sectionTitle: {
    color: '#FFFFFF',
    fontSize: 14, // text-sm
    marginBottom: 16, // mb-4
    textAlign: 'center',
  },
  colorRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16, // gap-4
  },
  colorButton: {
    width: 32, // w-8
    height: 32, // h-8
    borderRadius: 16, // rounded-full
  },
  colorButtonSelected: {
    borderWidth: 2,
    borderColor: '#FFFFFF',
    transform: [{ scale: 1.1 }],
  },
  promptsSection: {
    marginBottom: 32, // mb-8
  },
  promptsList: {
    // space-y-3 equivalent
  },
  promptButton: {
    width: '100%',
    padding: 16, // p-4
    borderRadius: 25, // rounded-full
    marginBottom: 12, // space-y-3
    borderWidth: 1,
  },
  promptButtonSelected: {
    backgroundColor: 'rgba(250, 204, 21, 0.2)', // bg-yellow-400 bg-opacity-20
    borderColor: '#FACC15', // border-yellow-400
  },
  promptButtonUnselected: {
    backgroundColor: 'rgba(31, 41, 55, 0.5)', // bg-gray-800 bg-opacity-50
    borderColor: '#4B5563', // border-gray-600
  },
  promptText: {
    fontSize: 14, // text-sm
    textAlign: 'left',
  },
  promptTextSelected: {
    color: '#FACC15', // text-yellow-400
  },
  promptTextUnselected: {
    color: '#FFFFFF', // text-white
  },
  nextButton: {
    width: '100%',
    paddingVertical: 12, // py-3
    paddingHorizontal: 24, // px-6
    borderRadius: 8, // rounded-lg
    backgroundColor: '#2563EB', // bg-blue-600
  },
  nextButtonDisabled: {
    backgroundColor: '#4B5563', // bg-gray-600
  },
  nextButtonText: {
    fontWeight: '500', // font-medium
    textAlign: 'center',
    fontSize: 16,
    color: '#FFFFFF', // text-white
  },
  nextButtonTextDisabled: {
    color: '#9CA3AF', // text-gray-400
  },
});
