import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView, Dimensions } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

export const SoundQuestionnaireScreen: React.FC = () => {
  const navigation = useNavigation();
  const [selectedPrompts, setSelectedPrompts] = useState<number[]>([]);

  const prompts = [
    "3 lyrics, line or phrase that resonates with me...",
    "I started hip if [artist] & [artist] made a record at [location]",
    "and a 30 second video or audio of you singing",
    "pick 3 words that represent your sound",
    "What would be the title of your Spotify playlist?",
    "When it comes to production, I lean toward...",
  ];

  const togglePrompt = (index: number) => {
    setSelectedPrompts((prev) => 
      prev.includes(index) 
        ? prev.filter((i) => i !== index) 
        : [...prev, index]
    );
  };

  const handleNext = () => {
    if (selectedPrompts.length === 0) return;
    
    console.log('Selected prompts:', selectedPrompts);
    // Navigate to energy questionnaire screen
    navigation.navigate('EnergyQuestionnaire' as never);
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#000000', '#166534', '#000000']} // from-black via-green-900 to-black
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={styles.gradient}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          {/* Header Section */}
          <View style={styles.headerSection}>
            <Text style={styles.headerTitle}>What's your sound?</Text>

            <View style={styles.scrollIndicator}>
              <Text style={styles.scrollText}>scroll</Text>
              <View style={styles.arrowContainer}>
                <Ionicons name="arrow-down" size={20} color="#FFFFFF" />
              </View>
            </View>
          </View>

          {/* Prompts Section */}
          <View style={styles.promptsSection}>
            <Text style={styles.instructionText}>pick at least 1 prompt to fill out</Text>

            <View style={styles.promptsList}>
              {prompts.map((prompt, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => togglePrompt(index)}
                  style={[
                    styles.promptButton,
                    selectedPrompts.includes(index) ? styles.promptButtonSelected : styles.promptButtonUnselected
                  ]}
                >
                  <Text style={[
                    styles.promptText,
                    selectedPrompts.includes(index) ? styles.promptTextSelected : styles.promptTextUnselected
                  ]}>
                    {prompt}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <TouchableOpacity
              style={[
                styles.nextButton,
                selectedPrompts.length === 0 && styles.nextButtonDisabled
              ]}
              onPress={handleNext}
              disabled={selectedPrompts.length === 0}
            >
              <Text style={styles.nextButtonText}>next</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  headerSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24, // px-6
    textAlign: 'center',
  },
  headerTitle: {
    fontSize: 36, // text-4xl
    fontWeight: 'bold',
    marginBottom: 32, // mb-8
    color: '#9ACD32', // YellowGreen
    textShadowColor: 'rgba(154, 205, 50, 0.5)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 20,
    textAlign: 'center',
  },
  scrollIndicator: {
    alignItems: 'center',
    marginTop: 64, // mt-16
  },
  scrollText: {
    color: '#FFFFFF',
    fontSize: 14, // text-sm
    marginBottom: 8, // mb-2
  },
  arrowContainer: {
    // Simple arrow without animation for now
    // You can add animations later if needed
  },
  promptsSection: {
    paddingHorizontal: 24, // px-6
    paddingBottom: 24, // pb-6
  },
  instructionText: {
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 24, // mb-6
    fontSize: 14, // text-sm
  },
  promptsList: {
    marginBottom: 24, // mb-6
  },
  promptButton: {
    width: '100%',
    padding: 16, // p-4
    borderRadius: 25, // rounded-full
    marginBottom: 12, // space-y-3
    borderWidth: 2,
  },
  promptButtonSelected: {
    backgroundColor: '#16A34A', // bg-green-600
    borderColor: '#4ADE80', // border-green-400
  },
  promptButtonUnselected: {
    backgroundColor: '#1F2937', // bg-gray-800
    borderColor: '#4B5563', // border-gray-600
  },
  promptText: {
    fontSize: 14, // text-sm
    textAlign: 'left',
  },
  promptTextSelected: {
    color: '#FFFFFF',
  },
  promptTextUnselected: {
    color: '#D1D5DB', // text-gray-300
  },
  nextButton: {
    width: '100%',
    backgroundColor: '#2563EB', // bg-blue-600
    paddingVertical: 12, // py-3
    paddingHorizontal: 24, // px-6
    borderRadius: 8, // rounded-lg
  },
  nextButtonDisabled: {
    backgroundColor: '#6B7280', // disabled state
    opacity: 0.5,
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontWeight: '500', // font-medium
    textAlign: 'center',
    fontSize: 16,
  },
});
