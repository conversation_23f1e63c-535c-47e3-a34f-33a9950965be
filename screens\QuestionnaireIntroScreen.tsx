import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, Dimensions } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';

export const QuestionnaireIntroScreen: React.FC = () => {
  const navigation = useNavigation();

  const handleReadyToMix = () => {
    // Navigate to the questionnaire screen
    console.log('Ready to mix pressed - navigating to questionnaire');
    navigation.navigate('Questionnaire' as never);
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#7F1D1D', '#DC2626', '#EF4444']} // from-red-900 via-red-700 to-red-500
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* MIX Branding with glow effect */}
          <View style={styles.brandingContainer}>
            <Text style={styles.theText}>the</Text>
            <Text style={styles.mixText}>MIX</Text>
          </View>

          {/* Description text */}
          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionText}>
              now we'll ask you a few questions to share for others to get to know who you are
            </Text>
          </View>

          {/* Ready button */}
          <TouchableOpacity style={styles.readyButton} onPress={handleReadyToMix}>
            <Text style={styles.readyButtonText}>ready to mix!</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32, // px-8
  },
  brandingContainer: {
    alignItems: 'center',
    marginBottom: 64, // mb-16
  },
  theText: {
    fontSize: 18, // text-lg
    fontWeight: '300', // font-light
    color: '#60A5FA', // text-blue-400
    marginBottom: 8, // mb-2
    textShadowColor: 'rgba(59, 130, 246, 0.5)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 10,
  },
  mixText: {
    fontSize: 96, // text-8xl equivalent
    fontWeight: 'bold', // font-bold
    color: '#60A5FA', // text-blue-400
    letterSpacing: 4, // tracking-wider
    textShadowColor: 'rgba(59, 130, 246, 0.8)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 20,
  },
  descriptionContainer: {
    alignItems: 'center',
    marginBottom: 64, // mb-16
    maxWidth: width * 0.7, // max-w-sm equivalent
  },
  descriptionText: {
    color: '#FFFFFF', // text-white
    fontSize: 18, // text-lg
    lineHeight: 28, // leading-relaxed
    textAlign: 'center',
  },
  readyButton: {
    backgroundColor: '#DC2626', // bg-red-600
    paddingHorizontal: 32, // px-8
    paddingVertical: 12, // py-3
    borderRadius: 8, // rounded-lg
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  readyButtonText: {
    color: '#FFFFFF', // text-white
    fontSize: 16,
    fontWeight: '500', // font-medium
    textAlign: 'center',
  },
});
