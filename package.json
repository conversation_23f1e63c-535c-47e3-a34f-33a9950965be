{"name": "zesh_mvp", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.7", "@supabase/supabase-js": "^2.55.0", "expo": "~53.0.20", "expo-av": "^15.1.7", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-url-polyfill": "^2.0.0", "react-native-screens": "~4.11.1", "react-native-safe-area-context": "5.4.0", "@expo/vector-icons": "^14.1.0", "expo-linear-gradient": "~14.1.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}